{"version": 1, "dependencies": {"net8.0-windows7.0": {"DalamudPackager": {"type": "Direct", "requested": "[11.0.0, )", "resolved": "11.0.0", "contentHash": "bjT7XUlhIJSmsE/O76b7weUX+evvGQctbQB8aKXt94o+oPWxHpCepxAGMs7Thow3AzCyqWs7cOpp9/2wcgRRQA=="}, "DotNet.ReproducibleBuilds": {"type": "Direct", "requested": "[1.1.1, )", "resolved": "1.1.1", "contentHash": "+H2t/t34h6mhEoUvHi8yGXyuZ2GjSovcGYehJrS2MDm2XgmPfZL2Sdxg+uL2lKgZ4M6tTwKHIlxOob2bgh0NRQ==", "dependencies": {"Microsoft.SourceLink.AzureRepos.Git": "1.1.1", "Microsoft.SourceLink.Bitbucket.Git": "1.1.1", "Microsoft.SourceLink.GitHub": "1.1.1", "Microsoft.SourceLink.GitLab": "1.1.1"}}, "Microsoft.Build.Tasks.Git": {"type": "Transitive", "resolved": "1.1.1", "contentHash": "AT3HlgTjsqHnWpBHSNeR0KxbLZD7bztlZVj7I8vgeYG9SYqbeFGh0TM/KVtC6fg53nrWHl3VfZFvb5BiQFcY6Q=="}, "Microsoft.SourceLink.AzureRepos.Git": {"type": "Transitive", "resolved": "1.1.1", "contentHash": "qB5urvw9LO2bG3eVAkuL+2ughxz2rR7aYgm2iyrB8Rlk9cp2ndvGRCvehk3rNIhRuNtQaeKwctOl1KvWiklv5w==", "dependencies": {"Microsoft.Build.Tasks.Git": "1.1.1", "Microsoft.SourceLink.Common": "1.1.1"}}, "Microsoft.SourceLink.Bitbucket.Git": {"type": "Transitive", "resolved": "1.1.1", "contentHash": "cDzxXwlyWpLWaH0em4Idj0H3AmVo3L/6xRXKssYemx+7W52iNskj/SQ4FOmfCb8YQt39otTDNMveCZzYtMoucQ==", "dependencies": {"Microsoft.Build.Tasks.Git": "1.1.1", "Microsoft.SourceLink.Common": "1.1.1"}}, "Microsoft.SourceLink.Common": {"type": "Transitive", "resolved": "1.1.1", "contentHash": "WMcGpWKrmJmzrNeuaEb23bEMnbtR/vLmvZtkAP5qWu7vQsY59GqfRJd65sFpBszbd2k/bQ8cs8eWawQKAabkVg=="}, "Microsoft.SourceLink.GitHub": {"type": "Transitive", "resolved": "1.1.1", "contentHash": "IaJGnOv/M7UQjRJks7B6p7pbPnOwisYGOIzqCz5ilGFTApZ3ktOR+6zJ12ZRPInulBmdAf1SrGdDG2MU8g6XTw==", "dependencies": {"Microsoft.Build.Tasks.Git": "1.1.1", "Microsoft.SourceLink.Common": "1.1.1"}}, "Microsoft.SourceLink.GitLab": {"type": "Transitive", "resolved": "1.1.1", "contentHash": "tvsg47DDLqqedlPeYVE2lmiTpND8F0hkrealQ5hYltSmvruy/Gr5nHAKSsjyw5L3NeM/HLMI5ORv7on/M4qyZw==", "dependencies": {"Microsoft.Build.Tasks.Git": "1.1.1", "Microsoft.SourceLink.Common": "1.1.1"}}}}}